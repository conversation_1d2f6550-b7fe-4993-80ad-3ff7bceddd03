<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'phone' => fake()->phoneNumber(),
            'agency_name' => fake()->company(),
            'license_number' => fake()->regexify('[A-Z0-9]{8}'),
            'budget_min' => fake()->numberBetween(100000, 500000),
            'budget_max' => fake()->numberBetween(500001, 1000000),
            'search_radius_km' => fake()->numberBetween(5, 50),
            'search_latitude' => fake()->latitude(),
            'search_longitude' => fake()->longitude(),
            'notify_property_type' => fake()->randomElement(['Residential', 'Commercial', 'Land']),
            'notify_budget_min' => fake()->numberBetween(100000, 500000),
            'notify_budget_max' => fake()->numberBetween(500001, 1000000),
            'bio' => fake()->paragraph(),
            'linkedin_url' => fake()->url(),
            'facebook_url' => fake()->url(),
            'twitter_url' => fake()->url(),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
