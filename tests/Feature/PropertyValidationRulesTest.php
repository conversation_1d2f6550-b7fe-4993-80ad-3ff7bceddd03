<?php

use App\Rules\PropertyValidationRules;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;

uses(RefreshDatabase::class);

test('create rules returns correct validation rules', function () {
    $rules = PropertyValidationRules::createRules();
    
    expect($rules)->toBeArray();
    expect($rules)->toHaveKeys([
        'property_sub_type_id',
        'listing_type',
        'title',
        'description',
        'price',
        'currency',
        'address_line_1',
        'city',
        'state_region',
        'zip_code',
        'bedrooms',
        'bathrooms',
        'square_footage',
        'latitude',
        'longitude',
        'selected_amenities',
        'status'
    ]);
});

test('land specific rules are included', function () {
    $rules = PropertyValidationRules::createRules();

    expect($rules)->toHaveKeys([
        'lot_size_acres',
        'zoning_details',
        'road_access'
    ]);

    expect($rules['lot_size_acres'])->toContain('nullable', 'numeric', 'min:0');
    expect($rules['zoning_details'])->toContain('nullable', 'string', 'max:255');
    expect($rules['road_access'])->toContain('nullable', 'boolean');
});

test('image rules returns correct validation rules', function () {
    $rules = PropertyValidationRules::imageRules();

    expect($rules)->toHaveKey('images.*');
    expect($rules['images.*'])->toContain('image', 'mimes:jpeg,png,jpg,gif,webp', 'max:10240');
});

test('messages returns validation messages', function () {
    $messages = PropertyValidationRules::messages();
    
    expect($messages)->toBeArray();
    expect($messages)->toHaveKeys([
        'property_sub_type_id.required',
        'title.required',
        'price.required',
        'lot_size_acres.numeric',
        'zoning_details.max',
        'road_access.boolean'
    ]);
});

test('attributes returns field attributes', function () {
    $attributes = PropertyValidationRules::attributes();
    
    expect($attributes)->toBeArray();
    expect($attributes)->toHaveKeys([
        'property_sub_type_id',
        'listing_type',
        'address_line_1',
        'state_region',
        'zip_code',
        'square_footage',
        'selected_amenities',
        'lot_size_acres',
        'zoning_details',
        'road_access'
    ]);
    
    expect($attributes['property_sub_type_id'])->toBe('property sub-type');
    expect($attributes['lot_size_acres'])->toBe('lot size (acres)');
    expect($attributes['road_access'])->toBe('road access');
});

test('validation rules work with valid data', function () {
    // Create required database records
    $propertyType = \App\Models\PropertyType::create(['name' => 'House']);
    $propertySubType = \App\Models\PropertySubType::create([
        'name' => 'Single Family',
        'property_type_id' => $propertyType->id
    ]);
    $amenity = \App\Models\Amenity::create(['name' => 'Pool']);

    $validData = [
        'property_type_id' => $propertyType->id,
        'property_sub_type_id' => $propertySubType->id,
        'listing_type' => 'for_sale',
        'title' => 'Test Property',
        'description' => 'A beautiful test property',
        'price' => 250000,
        'currency' => 'USD',
        'address_line_1' => '123 Test St',
        'city' => 'Test City',
        'state_region' => 'TS',
        'zip_code' => '12345',
        'bedrooms' => 3,
        'bathrooms' => 2,
        'square_footage' => 1500,
        'latitude' => 40.7128,
        'longitude' => -74.0060,
        'selected_amenities' => [$amenity->id],
        'status' => 'published',
        'lot_size_acres' => 0.5,
        'zoning_details' => 'Residential',
        'road_access' => true
    ];

    $validator = Validator::make($validData, PropertyValidationRules::createRules());

    if (!$validator->passes()) {
        dump($validator->errors()->all());
    }

    expect($validator->passes())->toBeTrue();
});

test('validation rules fail with invalid data', function () {
    $invalidData = [
        'property_type_id' => null, // Required field missing
        'property_sub_type_id' => null, // Required field missing
        'listing_type' => 'invalid_type', // Invalid enum value
        'title' => '', // Required field empty
        'price' => -1000, // Negative price
        'currency' => 'INVALID', // Invalid currency
        'bedrooms' => -1, // Negative bedrooms
        'bathrooms' => -1, // Negative bathrooms
        'square_footage' => -100, // Negative square footage
        'latitude' => 200, // Invalid latitude
        'longitude' => 200, // Invalid longitude
        'selected_amenities' => 'not_array', // Should be array
        'status' => 'invalid_status', // Invalid status
        'lot_size_acres' => -1, // Negative lot size
        'road_access' => 'invalid_access' // Invalid road access (should be boolean)
    ];
    
    $validator = Validator::make($invalidData, PropertyValidationRules::createRules());
    
    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('property_type_id'))->toBeTrue();
    expect($validator->errors()->has('property_sub_type_id'))->toBeTrue();
    expect($validator->errors()->has('listing_type'))->toBeTrue();
    expect($validator->errors()->has('title'))->toBeTrue();
    expect($validator->errors()->has('price'))->toBeTrue();
    expect($validator->errors()->has('lot_size_acres'))->toBeTrue();
    expect($validator->errors()->has('road_access'))->toBeTrue();
});

test('land specific validation works correctly', function () {
    $landData = [
        'lot_size_acres' => -1, // Negative (invalid)
        'zoning_details' => str_repeat('a', 256), // Too long (max 255)
        'road_access' => 'invalid' // Invalid (should be boolean)
    ];
    
    $validator = Validator::make($landData, PropertyValidationRules::createRules());
    
    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('lot_size_acres'))->toBeTrue();
    expect($validator->errors()->has('zoning_details'))->toBeTrue();
    expect($validator->errors()->has('road_access'))->toBeTrue();
});

test('nullable fields accept null values', function () {
    // Create required database records
    $propertyType = \App\Models\PropertyType::create(['name' => 'House']);
    $propertySubType = \App\Models\PropertySubType::create([
        'name' => 'Single Family',
        'property_type_id' => $propertyType->id
    ]);

    $dataWithNulls = [
        'property_type_id' => $propertyType->id,
        'property_sub_type_id' => $propertySubType->id,
        'listing_type' => 'for_sale',
        'title' => 'Test Property',
        'description' => 'Test description',
        'price' => 100000,
        'currency' => 'USD',
        'address_line_1' => '123 Test St',
        'city' => 'Test City',
        'state_region' => 'TS',
        'zip_code' => '12345',
        'bedrooms' => 2, // Required in create rules
        'bathrooms' => 1, // Required in create rules
        'square_footage' => 1000, // Required in create rules
        'selected_amenities' => [],
        'status' => 'draft',
        // Nullable fields
        'latitude' => null,
        'longitude' => null,
        'lot_size_acres' => null,
        'zoning_details' => null,
        'road_access' => null
    ];

    $validator = Validator::make($dataWithNulls, PropertyValidationRules::createRules());

    if (!$validator->passes()) {
        dump($validator->errors()->all());
    }

    expect($validator->passes())->toBeTrue();
});

test('image validation works correctly', function () {
    // This would require actual file uploads to test properly
    // For now, we'll just verify the rules structure
    $imageRules = PropertyValidationRules::imageRules();

    expect($imageRules['images.*'])->toContain('image');
    expect($imageRules['images.*'])->toContain('mimes:jpeg,png,jpg,gif,webp');
    expect($imageRules['images.*'])->toContain('max:10240');
});
