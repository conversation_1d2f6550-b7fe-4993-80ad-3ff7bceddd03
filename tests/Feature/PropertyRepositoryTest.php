<?php

use App\Models\Property;
use App\Models\PropertyType;
use App\Models\PropertySubType;
use App\Models\User;
use App\Services\PropertyRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->repository = app(PropertyRepository::class);

    // Create roles first
    \Spatie\Permission\Models\Role::create(['name' => 'admin']);
    \Spatie\Permission\Models\Role::create(['name' => 'agent']);
    \Spatie\Permission\Models\Role::create(['name' => 'seeker']);

    // Create test users with roles
    $this->admin = User::factory()->create();
    $this->admin->assignRole('admin');

    $this->agent = User::factory()->create();
    $this->agent->assignRole('agent');

    $this->seeker = User::factory()->create();
    $this->seeker->assignRole('seeker');

    // Create property types and subtypes
    $this->propertyType = PropertyType::create(['name' => 'House']);
    $this->propertySubType = PropertySubType::create([
        'name' => 'Single Family',
        'property_type_id' => $this->propertyType->id
    ]);
});

test('base query includes required relationships', function () {
    $query = $this->repository->baseQuery();
    
    expect($query->getEagerLoads())->toHaveKeys([
        'user', 'propertySubType.propertyType', 'media'
    ]);
});

test('keyword search filters properties correctly', function () {
    // Create test properties
    $property1 = Property::factory()->create([
        'title' => 'Beautiful Downtown Apartment',
        'city' => 'New York',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published'
    ]);
    
    $property2 = Property::factory()->create([
        'title' => 'Suburban House',
        'city' => 'Los Angeles',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published'
    ]);
    
    // Test keyword search
    $query = $this->repository->baseQuery();
    $filteredQuery = $this->repository->applyKeywordSearch($query, 'Downtown');
    $results = $filteredQuery->get();
    
    expect($results)->toHaveCount(1);
    expect($results->first()->title)->toBe('Beautiful Downtown Apartment');
});

test('property type filter works correctly', function () {
    // Create another property type
    $apartmentType = PropertyType::create(['name' => 'Apartment']);
    $apartmentSubType = PropertySubType::create([
        'name' => 'Studio',
        'property_type_id' => $apartmentType->id
    ]);

    // Create properties with different types
    $house = Property::factory()->create([
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published'
    ]);

    $apartment = Property::factory()->create([
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $apartmentSubType->id,
        'status' => 'published'
    ]);

    // Test property type filter
    $query = $this->repository->baseQuery();
    $filteredQuery = $this->repository->applyPropertyTypeFilter($query, $this->propertyType->id, null);
    $results = $filteredQuery->get();

    expect($results)->toHaveCount(1);
    expect($results->first()->id)->toBe($house->id);
});

test('published only filter works correctly', function () {
    // Create properties with different statuses
    $published = Property::factory()->create([
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published'
    ]);
    
    $draft = Property::factory()->create([
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'draft'
    ]);
    
    // Test published only filter
    $query = $this->repository->baseQuery();
    $filteredQuery = $this->repository->publishedOnly($query);
    $results = $filteredQuery->get();
    
    expect($results)->toHaveCount(1);
    expect($results->first()->status)->toBe('published');
});

test('build search query applies multiple filters', function () {
    // Create test properties
    $matchingProperty = Property::factory()->create([
        'title' => 'Downtown House for Sale',
        'listing_type' => 'for_sale',
        'price' => 250000,
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published'
    ]);
    
    $nonMatchingProperty = Property::factory()->create([
        'title' => 'Suburban Apartment for Rent',
        'listing_type' => 'for_rent',
        'price' => 2000,
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published'
    ]);
    
    // Test combined filters
    $results = $this->repository->buildSearchQuery([
        'keywords' => 'Downtown',
        'listing_type' => 'for_sale',
        'min_price' => 200000,
        'max_price' => 300000
    ])->get();
    
    expect($results)->toHaveCount(1);
    expect($results->first()->id)->toBe($matchingProperty->id);
});

test('get user properties returns only user properties', function () {
    // Create properties for different users
    $userProperty = Property::factory()->create([
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id
    ]);

    $otherProperty = Property::factory()->create([
        'user_id' => $this->seeker->id,
        'property_sub_type_id' => $this->propertySubType->id
    ]);

    $results = $this->repository->getUserProperties($this->agent->id);

    expect($results->count())->toBe(1);
    expect($results->first()->id)->toBe($userProperty->id);
});

test('to javascript format converts properties correctly', function () {
    $property = Property::factory()->create([
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'latitude' => 40.7128,
        'longitude' => -74.0060
    ]);

    $collection = collect([$property]);
    $jsFormat = $this->repository->toJavaScriptFormat($collection);

    expect($jsFormat)->toBeArray();
    expect($jsFormat[0])->toHaveKeys(['id', 'title', 'latitude', 'longitude']);
    expect($jsFormat[0]['latitude'])->toBe(40.7128);
    expect($jsFormat[0]['longitude'])->toBe(-74.0060);
});
