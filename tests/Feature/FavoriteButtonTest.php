<?php

use App\Models\Property;
use App\Models\PropertyType;
use App\Models\PropertySubType;
use App\Models\User;
use Livewire\Livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('user can favorite a property', function () {
    // Create required data
    $propertyType = PropertyType::create(['name' => 'Residential']);
    $propertySubType = PropertySubType::create([
        'name' => 'House',
        'property_type_id' => $propertyType->id
    ]);

    $user = User::factory()->create();
    $property = Property::factory()->create([
        'user_id' => $user->id,
        'property_sub_type_id' => $propertySubType->id
    ]);
    
    $this->actingAs($user);
    
    Livewire::test(\App\Livewire\FavoriteButton::class, ['property' => $property])
        ->assertSet('isFavorited', false)
        ->call('toggleFavorite')
        ->assertSet('isFavorited', true);
    
    expect($user->favoritedProperties()->where('property_id', $property->id)->exists())->toBeTrue();
});

test('user can unfavorite a property', function () {
    // Create required data
    $propertyType = PropertyType::factory()->create();
    $propertySubType = PropertySubType::factory()->create(['property_type_id' => $propertyType->id]);

    $user = User::factory()->create();
    $property = Property::factory()->create([
        'user_id' => $user->id,
        'property_sub_type_id' => $propertySubType->id
    ]);
    
    // First favorite the property
    $user->favoritedProperties()->attach($property->id);
    
    $this->actingAs($user);
    
    Livewire::test(\App\Livewire\FavoriteButton::class, ['property' => $property])
        ->assertSet('isFavorited', true)
        ->call('toggleFavorite')
        ->assertSet('isFavorited', false);
    
    expect($user->favoritedProperties()->where('property_id', $property->id)->exists())->toBeFalse();
});

test('guest user is redirected to login when trying to favorite', function () {
    // Create required data
    $propertyType = PropertyType::factory()->create();
    $propertySubType = PropertySubType::factory()->create(['property_type_id' => $propertyType->id]);
    $user = User::factory()->create();

    $property = Property::factory()->create([
        'user_id' => $user->id,
        'property_sub_type_id' => $propertySubType->id
    ]);
    
    Livewire::test(\App\Livewire\FavoriteButton::class, ['property' => $property])
        ->assertSet('isFavorited', false)
        ->call('toggleFavorite')
        ->assertRedirect(route('login'));
});
