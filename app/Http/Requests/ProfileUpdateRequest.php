<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->id),
            ],
            'phone' => ['nullable', 'string', 'max:20'],
            'profile_photo' => ['nullable', 'image', 'mimes:jpeg,png,gif,webp', 'max:2048'],
            'bio' => ['nullable', 'string', 'max:500'],
            'linkedin_url' => ['nullable', 'url', 'max:255'],
            'facebook_url' => ['nullable', 'url', 'max:255'],
            'twitter_url' => ['nullable', 'url', 'max:255'],
        ];

        if ($this->user()->hasRole('agent')) {
            $rules['phone'] = ['required', 'string', 'max:20'];
            $rules['agency_name'] = ['nullable', 'string', 'max:255'];
            $rules['license_number'] = ['nullable', 'string', 'max:255'];
        }

        if ($this->user()->hasRole('seeker')) {
            $rules['budget_min'] = ['nullable', 'integer', 'min:0'];
            $rules['budget_max'] = ['nullable', 'integer', 'min:0', 'gte:budget_min'];
            $rules['search_radius_km'] = ['nullable', 'integer', 'min:0'];
            $rules['search_latitude'] = ['nullable', 'numeric', 'between:-90,90'];
            $rules['search_longitude'] = ['nullable', 'numeric', 'between:-180,180'];
            $rules['notify_property_type'] = ['nullable', 'string', 'max:255'];
            $rules['notify_budget_min'] = ['nullable', 'numeric', 'min:0'];
            $rules['notify_budget_max'] = ['nullable', 'numeric', 'min:0', 'gte:notify_budget_min'];
        }

        return $rules;
    }
}
