<?php

namespace App\Rules;

class PropertyValidationRules
{
    /**
     * Get validation rules for property creation
     */
    public static function createRules(): array
    {
        return [
            'property_type_id' => ['required', 'integer', 'exists:property_types,id'],
            'property_sub_type_id' => ['required', 'integer', 'exists:property_sub_types,id'],
            'listing_type' => ['required', 'string', 'in:for_sale,for_rent'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:5000'],
            'price' => ['required', 'numeric', 'min:0', 'max:999999999.99'],
            'currency' => ['nullable', 'string', 'max:10', 'in:KES,USD,EUR,GBP,CAD,AUD'],
            'address_line_1' => ['required', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state_region' => ['nullable', 'string', 'max:255'],
            'zip_code' => ['nullable', 'string', 'max:20'],
            'bedrooms' => ['required', 'integer', 'min:0', 'max:50'],
            'bathrooms' => ['required', 'integer', 'min:0', 'max:50'],
            'square_footage' => ['required', 'integer', 'min:0', 'max:999999'],
            'latitude' => ['nullable', 'numeric', 'between:-90,90'],
            'longitude' => ['nullable', 'numeric', 'between:-180,180'],
            'status' => ['required', 'string', 'in:draft,published'],
            'selected_amenities' => ['nullable', 'array'],
            'selected_amenities.*' => ['integer', 'exists:amenities,id'],
            // Land-specific attributes
            'lot_size_acres' => ['nullable', 'numeric', 'min:0', 'max:999999'],
            'zoning_details' => ['nullable', 'string', 'max:255'],
            'road_access' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Get validation rules for property updates
     */
    public static function updateRules(): array
    {
        $rules = self::createRules();
        
        // Allow additional status values for updates
        $rules['status'] = ['required', 'string', 'in:draft,published,sold,rented,under_offer'];
        
        // Make some fields optional for updates
        $rules['bedrooms'] = ['nullable', 'integer', 'min:0', 'max:50'];
        $rules['bathrooms'] = ['nullable', 'integer', 'min:0', 'max:50'];
        $rules['square_footage'] = ['nullable', 'integer', 'min:0', 'max:999999'];
        
        return $rules;
    }

    /**
     * Get validation rules for image uploads
     */
    public static function imageRules(): array
    {
        return [
            'images' => ['nullable', 'array', 'max:10'],
            'images.*' => ['image', 'mimes:jpeg,png,jpg,gif,webp', 'max:10240'], // 10MB max
        ];
    }

    /**
     * Get validation rules for status updates only
     */
    public static function statusUpdateRules(): array
    {
        return [
            'status' => ['required', 'string', 'in:draft,published,sold,rented,under_offer'],
        ];
    }

    /**
     * Get validation rules for admin property updates (more permissive)
     */
    public static function adminUpdateRules(): array
    {
        $rules = self::updateRules();
        
        // Admin can set featured status
        $rules['is_featured'] = ['nullable', 'boolean'];
        
        return $rules;
    }

    /**
     * Get custom validation messages
     */
    public static function messages(): array
    {
        return [
            'property_type_id.required' => 'Please select a property type.',
            'property_type_id.exists' => 'The selected property type is invalid.',
            'property_sub_type_id.required' => 'Please select a property sub-type.',
            'property_sub_type_id.exists' => 'The selected property sub-type is invalid.',
            'listing_type.in' => 'Listing type must be either for sale or for rent.',
            'title.required' => 'Property title is required.',
            'title.max' => 'Property title cannot exceed 255 characters.',
            'description.required' => 'Property description is required.',
            'description.max' => 'Property description cannot exceed 5000 characters.',
            'price.required' => 'Property price is required.',
            'price.numeric' => 'Property price must be a valid number.',
            'price.min' => 'Property price cannot be negative.',
            'price.max' => 'Property price is too large.',
            'currency.in' => 'Currency must be one of: KES, USD, EUR, GBP, CAD, AUD.',
            'address_line_1.required' => 'Address is required.',
            'city.required' => 'City is required.',
            'bedrooms.required' => 'Number of bedrooms is required.',
            'bedrooms.integer' => 'Number of bedrooms must be a whole number.',
            'bedrooms.min' => 'Number of bedrooms cannot be negative.',
            'bedrooms.max' => 'Number of bedrooms cannot exceed 50.',
            'bathrooms.required' => 'Number of bathrooms is required.',
            'bathrooms.integer' => 'Number of bathrooms must be a whole number.',
            'bathrooms.min' => 'Number of bathrooms cannot be negative.',
            'bathrooms.max' => 'Number of bathrooms cannot exceed 50.',
            'square_footage.required' => 'Square footage is required.',
            'square_footage.integer' => 'Square footage must be a whole number.',
            'square_footage.min' => 'Square footage cannot be negative.',
            'square_footage.max' => 'Square footage is too large.',
            'latitude.between' => 'Latitude must be between -90 and 90.',
            'longitude.between' => 'Longitude must be between -180 and 180.',
            'status.in' => 'Invalid property status.',
            'selected_amenities.array' => 'Amenities must be provided as a list.',
            'selected_amenities.*.exists' => 'One or more selected amenities are invalid.',
            'images.max' => 'You can upload a maximum of 10 images.',
            'images.*.image' => 'All uploaded files must be images.',
            'images.*.mimes' => 'Images must be in JPEG, PNG, JPG, GIF, or WebP format.',
            'images.*.max' => 'Each image must be smaller than 10MB.',
            'lot_size_acres.numeric' => 'Lot size must be a valid number.',
            'lot_size_acres.min' => 'Lot size cannot be negative.',
            'lot_size_acres.max' => 'Lot size is too large.',
            'zoning_details.max' => 'Zoning details cannot exceed 255 characters.',
            'road_access.boolean' => 'Road access must be yes or no.',
        ];
    }

    /**
     * Get validation attributes for better error messages
     */
    public static function attributes(): array
    {
        return [
            'property_type_id' => 'property type',
            'property_sub_type_id' => 'property sub-type',
            'listing_type' => 'listing type',
            'address_line_1' => 'address',
            'state_region' => 'state/region',
            'zip_code' => 'ZIP code',
            'square_footage' => 'square footage',
            'selected_amenities' => 'amenities',
            'is_featured' => 'featured status',
            'lot_size_acres' => 'lot size (acres)',
            'zoning_details' => 'zoning details',
            'road_access' => 'road access',
        ];
    }
}
