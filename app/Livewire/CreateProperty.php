<?php

namespace App\Livewire;

use App\Models\Amenity;
use App\Models\Property;
use App\Models\PropertyType;
use App\Models\PropertySubType;
use App\Rules\PropertyValidationRules;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Collection;

class CreateProperty extends Component
{
    use WithFileUploads;

    // Form fields
    public $property_type_id = '';
    public $property_sub_type_id = '';
    public $listing_type = 'for_sale';
    public $title = '';
    public $description = '';
    public $price = '';
    public $address_line_1 = '';
    public $city = '';
    public $state_region = '';
    public $zip_code = '';
    public $bedrooms = '';
    public $bathrooms = '';
    public $square_footage = '';
    public $latitude = '';
    public $longitude = '';
    public $status = 'published';
    public $selected_amenities = [];
    public $images = [];

    // Data for populating form dropdowns
    public Collection $property_types;
    public Collection $amenities;
    public Collection $property_sub_types;

    public function mount()
    {
        $this->property_types = PropertyType::all();
        $this->amenities = Amenity::all();
        $this->property_sub_types = collect();
    }

    public function updatedPropertyTypeId($value)
    {
        if ($value) {
            $this->property_sub_types = PropertySubType::where('property_type_id', $value)->get();
        } else {
            $this->property_sub_types = collect();
        }
        $this->property_sub_type_id = ''; // Reset sub-type when parent type changes
    }

    protected function rules()
    {
        return array_merge(
            PropertyValidationRules::createRules(),
            PropertyValidationRules::imageRules()
        );
    }

    protected function messages()
    {
        return PropertyValidationRules::messages();
    }

    protected function validationAttributes()
    {
        return PropertyValidationRules::attributes();
    }

    public function store()
    {
        $this->validate();

        $listerType = 'Unknown';
        $user = Auth::user();
        if ($user && $user->roles) {
            $roleNames = $user->roles->pluck('name')->map(function ($name) {
                return strtolower($name);
            });
            if ($roleNames->contains('developer')) {
                $listerType = 'Developer';
            } elseif ($roleNames->contains('owner')) {
                $listerType = 'Owner';
            } elseif ($roleNames->contains('agent')) {
                $listerType = 'Agent';
            }
        }

        $property = Property::create([
            'property_sub_type_id' => $this->property_sub_type_id,
            'listing_type' => $this->listing_type,
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'address_line_1' => $this->address_line_1,
            'city' => $this->city,
            'state_region' => $this->state_region,
            'zip_code' => $this->zip_code,
            'bedrooms' => $this->bedrooms,
            'bathrooms' => $this->bathrooms,
            'square_footage' => $this->square_footage,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'lister_type' => $listerType,
        ]);

        // Set sensitive fields explicitly
        $property->user_id = Auth::id();
        $property->status = $this->status;
        $property->save();

        // Attach amenities
        $property->amenities()->attach($this->selected_amenities);

        // Add images using Spatie MediaLibrary
        foreach ($this->images as $image) {
            $property->addMedia($image->getRealPath())
                     ->usingName($image->getClientOriginalName())
                     ->toMediaCollection('gallery');
        }

        // Dispatch the PropertyIsLive event for real-time notifications
        event(new \App\Events\PropertyIsLive($property));

        session()->flash('message', 'Property successfully created.');

        return $this->redirect(route('properties.show', $property), navigate: true);
    }

    public function render()
    {
        return view('livewire.create-property');
    }
}
