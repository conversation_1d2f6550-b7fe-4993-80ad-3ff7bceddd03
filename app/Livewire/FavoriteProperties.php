<?php

namespace App\Livewire;

use App\Services\PropertyRepository;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class FavoriteProperties extends Component
{
    public $properties;
    protected PropertyRepository $propertyRepository;

    public function boot(PropertyRepository $propertyRepository)
    {
        $this->propertyRepository = $propertyRepository;
    }

    public function mount()
    {
        $this->loadProperties();
    }

    public function loadProperties()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        $this->properties = $this->propertyRepository->getUserFavorites($user);
    }

    public function render()
    {
        return view('livewire.favorite-properties');
    }
}
