<?php

namespace App\Livewire;

use App\Models\Property;
use App\Services\PropertyRepository;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class MyProperties extends Component
{
    use WithPagination;

    protected PropertyRepository $propertyRepository;

    public function boot(PropertyRepository $propertyRepository)
    {
        $this->propertyRepository = $propertyRepository;
    }

    public function deleteProperty(Property $property)
    {
        if (Auth::id() !== $property->user_id) {
            abort(403);
        }

        // Media files are automatically deleted by Spatie Media Library when the model is deleted
        $property->delete();
        session()->flash('message', 'Property deleted successfully!');
    }

    public function updatePropertyStatus(Property $property, $status)
    {
        if (Auth::id() !== $property->user_id) {
            abort(403);
        }

        $property->status = $status;
        $property->save();
        session()->flash('message', 'Property status updated successfully!');
    }

    public function render()
    {
        $properties = $this->propertyRepository->getUserProperties(Auth::id());

        return view('livewire.my-properties', [
            'properties' => $properties,
        ]);
    }
}
