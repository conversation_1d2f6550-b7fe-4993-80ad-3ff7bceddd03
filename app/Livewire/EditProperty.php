<?php

namespace App\Livewire;

use App\Models\Property;
use App\Models\PropertyType;
use App\Models\PropertySubType;
use App\Models\Amenity;
use App\Rules\PropertyValidationRules;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;

class EditProperty extends Component
{
    use WithFileUploads;

    // Flattened properties
    public $property_id;
    public $property_type_id;
    public $property_sub_type_id;
    public $listing_type;
    public $title;
    public $description;
    public $price;
    public $currency;
    public $address_line_1;
    public $city;
    public $state_region;
    public $zip_code;
    public $latitude;
    public $longitude;
    public $bedrooms;
    public $bathrooms;
    public $square_footage;
    public $status;
    
    public $images = [];
    public $selected_amenities = [];
    public $property_types;
    public $property_sub_types;
    public $amenities;

    public function mount(Property $property)
    {
        $this->authorize('update', $property);

        // Eager load relationships to avoid N+1 queries
        $property->load(['propertySubType.propertyType', 'amenities']);

        // Set individual properties from the property model
        $this->property_id = $property->id;
        $this->property_type_id = $property->propertySubType->propertyType->id;
        $this->property_sub_type_id = $property->propertySubType->id;
        $this->listing_type = $property->listing_type;
        $this->title = $property->title;
        $this->description = $property->description;
        $this->price = $property->price;
        $this->currency = $property->currency;
        $this->address_line_1 = $property->address_line_1;
        $this->city = $property->city;
        $this->state_region = $property->state_region;
        $this->zip_code = $property->zip_code;
        $this->latitude = $property->latitude;
        $this->longitude = $property->longitude;
        $this->bedrooms = $property->bedrooms;
        $this->bathrooms = $property->bathrooms;
        $this->square_footage = $property->square_footage;
        $this->status = $property->status;
        
        $this->selected_amenities = $property->amenities->pluck('id')->toArray();
        $this->property_types = PropertyType::all();
        $this->amenities = Amenity::all();
        $this->property_sub_types = $this->property_type_id 
            ? PropertySubType::where('property_type_id', $this->property_type_id)->get() 
            : collect([]);
    }

    public function updatedPropertyTypeId($value)
    {
        $this->property_sub_types = $value 
            ? PropertySubType::where('property_type_id', $value)->get() 
            : collect([]);
        $this->property_sub_type_id = null;
    }

    public function rules()
    {
        return array_merge(
            PropertyValidationRules::updateRules(),
            PropertyValidationRules::imageRules()
        );
    }

    protected function messages()
    {
        return PropertyValidationRules::messages();
    }

    protected function validationAttributes()
    {
        return PropertyValidationRules::attributes();
    }

    public function update()
    {
        $this->validate();

        $property = Property::find($this->property_id);
        $this->authorize('update', $property);
        $property->property_type_id = $this->property_type_id;
        $property->property_sub_type_id = $this->property_sub_type_id;
        $property->listing_type = $this->listing_type;
        $property->title = $this->title;
        $property->description = $this->description;
        $property->price = $this->price;
        $property->currency = $this->currency;
        $property->address_line_1 = $this->address_line_1;
        $property->city = $this->city;
        $property->state_region = $this->state_region;
        $property->zip_code = $this->zip_code;
        $property->latitude = $this->latitude;
        $property->longitude = $this->longitude;
        $property->bedrooms = $this->bedrooms;
        $property->bathrooms = $this->bathrooms;
        $property->square_footage = $this->square_footage;
        $property->status = $this->status;
        $property->save();

        // Handle image uploads using Spatie MediaLibrary (consistent with CreateProperty)
        if ($this->images) {
            foreach ($this->images as $image) {
                $property->addMedia($image->getRealPath())
                         ->usingName($image->getClientOriginalName())
                         ->toMediaCollection('gallery');
            }
        }

        // Sync amenities
        $property->amenities()->sync($this->selected_amenities);

        // Dispatch PropertyIsLive event if property is being published
        if ($this->status === 'published') {
            event(new \App\Events\PropertyIsLive($property));
        }

        session()->flash('message', 'Property updated successfully!');
        return redirect()->route('agent.properties.index');
    }

    public function render()
    {
        $property = Property::find($this->property_id);

        return view('livewire.edit-property', [
            'property' => $property,
            'property_types' => $this->property_types,
            'property_sub_types' => $this->property_sub_types,
            'amenities' => $this->amenities
        ]);
    }
}
