<?php

namespace App\Livewire\Profile;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateProfileInformation extends Component
{
    use WithFileUploads;

    public $name;
    public $email;
    public $phone;
    public $agency_name;
    public $license_number;
    public $budget_min;
    public $budget_max;
    public $search_radius_km;
    public $search_latitude;
    public $search_longitude;
    public $notify_property_type;
    public $notify_budget_min;
    public $notify_budget_max;
    public $bio;
    public $linkedin_url;
    public $facebook_url;
    public $twitter_url;
    public $profile_photo;

    public function mount()
    {
        $user = Auth::user();
        $this->name = $user->name;
        $this->email = $user->email;
        $this->phone = $user->phone;
        $this->agency_name = $user->agency_name;
        $this->license_number = $user->license_number;
        $this->budget_min = $user->budget_min;
        $this->budget_max = $user->budget_max;
        $this->search_radius_km = $user->search_radius_km;
        $this->search_latitude = $user->search_latitude;
        $this->search_longitude = $user->search_longitude;
        $this->notify_property_type = $user->notify_property_type;
        $this->notify_budget_min = $user->notify_budget_min;
        $this->notify_budget_max = $user->notify_budget_max;
        $this->bio = $user->bio;
        $this->linkedin_url = $user->linkedin_url;
        $this->facebook_url = $user->facebook_url;
        $this->twitter_url = $user->twitter_url;
    }

    public function updateProfileInformation()
    {
        $user = Auth::user();

        $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:255'],
            'agency_name' => ['nullable', 'string', 'max:255'],
            'license_number' => ['nullable', 'string', 'max:255'],
            'budget_min' => ['nullable', 'integer', 'min:0'],
            'budget_max' => ['nullable', 'integer', 'min:0'],
            'search_radius_km' => ['nullable', 'integer', 'min:0'],
            'search_latitude' => ['nullable', 'numeric', 'between:-90,90'],
            'search_longitude' => ['nullable', 'numeric', 'between:-180,180'],
            'notify_property_type' => ['nullable', 'string', 'max:255'],
            'notify_budget_min' => ['nullable', 'numeric', 'min:0'],
            'notify_budget_max' => ['nullable', 'numeric', 'min:0'],
            'bio' => ['nullable', 'string'],
            'linkedin_url' => ['nullable', 'url', 'max:255'],
            'facebook_url' => ['nullable', 'url', 'max:255'],
            'twitter_url' => ['nullable', 'url', 'max:255'],
            'profile_photo' => ['nullable', 'image', 'max:1024'], // 1MB Max
        ]);

        $user->fill([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'agency_name' => $this->agency_name,
            'license_number' => $this->license_number,
            'budget_min' => $this->budget_min,
            'budget_max' => $this->budget_max,
            'search_radius_km' => $this->search_radius_km,
            'search_latitude' => $this->search_latitude,
            'search_longitude' => $this->search_longitude,
            'notify_property_type' => $this->notify_property_type,
            'notify_budget_min' => $this->notify_budget_min,
            'notify_budget_max' => $this->notify_budget_max,
            'bio' => $this->bio,
            'linkedin_url' => $this->linkedin_url,
            'facebook_url' => $this->facebook_url,
            'twitter_url' => $this->twitter_url,
        ]);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        $user->save();

        if ($this->profile_photo) {
            $user->clearMediaCollection('profile_photo');
            $user->addMedia($this->profile_photo->getRealPath())
                 ->usingFileName($this->profile_photo->hashName())
                 ->toMediaCollection('profile_photo');

            // Reset the file input after successful upload
            $this->profile_photo = null;
        }

        session()->flash('status', 'profile-updated');
        $this->dispatch('profile-updated', name: $user->name);
    }

    public function sendVerification()
    {
        Auth::user()->sendEmailVerificationNotification();

        $this->dispatch('verification-link-sent');
    }

    public function render()
    {
        return view('livewire.profile.update-profile-information', [
            'user' => Auth::user(),
        ]);
    }
}