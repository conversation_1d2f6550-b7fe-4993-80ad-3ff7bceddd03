<?php

namespace App\Livewire\Admin;

use App\Models\Property;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class FavoriteManager extends Component
{
    public $properties;
    public $favorites;

    public function mount()
    {
        $this->loadProperties();
    }

    public function loadProperties()
    {
        $this->properties = Property::with(['favoritedBy', 'propertySubType.propertyType', 'media'])->get();
        /** @var User $user */
        $user = Auth::user();
        $this->favorites = $user->favoritedProperties()->pluck('property_id')->toArray();
    }

    public function toggleFavorite($propertyId)
    {
        /** @var User $user */
        $user = Auth::user();
        if (in_array($propertyId, $this->favorites)) {
            $user->favoritedProperties()->detach($propertyId);
        } else {
            $user->favoritedProperties()->attach($propertyId);
        }
        $this->loadProperties();
    }

    public function render()
    {
        return view('livewire.admin.favorite-manager');
    }
}
