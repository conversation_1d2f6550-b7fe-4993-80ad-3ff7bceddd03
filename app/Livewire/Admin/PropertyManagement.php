<?php

namespace App\Livewire\Admin;

use App\Models\Property;
use App\Services\PropertyRepository;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[Layout('components.layouts.admin', ['title' => 'Property Management'])]
class PropertyManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $propertyTypeFilter = '';
    public $propertySubTypeFilter = '';
    public $statusFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';

    protected PropertyRepository $propertyRepository;

    public function boot(PropertyRepository $propertyRepository)
    {
        $this->propertyRepository = $propertyRepository;
    }

    protected $queryString = [
        'search' => ['except' => ''],
        'propertyTypeFilter' => ['except' => ''],
        'propertySubTypeFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updating($key)
    {
        if (in_array($key, ['search', 'propertyTypeFilter', 'propertySubTypeFilter', 'statusFilter', 'sortField', 'sortDirection'])) {
            $this->resetPage();
        }
    }

    public function updatedPropertyTypeFilter()
    {
        $this->propertySubTypeFilter = '';
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        // Build admin query using repository
        $properties = $this->propertyRepository->buildAdminQuery([
            'search' => $this->search,
            'propertyTypeFilter' => $this->propertyTypeFilter,
            'propertySubTypeFilter' => $this->propertySubTypeFilter,
            'statusFilter' => $this->statusFilter,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
        ]);

        $propertyTypes = $this->propertyRepository->getPropertyTypes();
        $propertySubTypes = $this->propertyRepository->getPropertySubTypes($this->propertyTypeFilter);

        return view('livewire.admin.property-management', [
            'properties' => $properties->paginate(10),
            'propertyTypes' => $propertyTypes,
            'propertySubTypes' => $propertySubTypes,
        ]);
    }

    public function getMediaStatus($property)
    {
        $mediaCount = $property->getMedia('gallery')->count();

        if ($mediaCount === 0) {
            return [
                'status' => 'no-media',
                'label' => 'No Images',
                'color' => 'gray',
                'count' => 0,
                'converted' => 0
            ];
        }

        $mediaWithConversions = $property->getMedia('gallery')
            ->filter(function ($media) {
                return count($media->generated_conversions) > 0;
            })->count();

        $conversionRate = ($mediaWithConversions / $mediaCount) * 100;

        if ($conversionRate === 100) {
            return [
                'status' => 'complete',
                'label' => 'Complete',
                'color' => 'green',
                'count' => $mediaCount,
                'converted' => $mediaWithConversions
            ];
        } elseif ($conversionRate >= 50) {
            return [
                'status' => 'partial',
                'label' => 'Partial',
                'color' => 'yellow',
                'count' => $mediaCount,
                'converted' => $mediaWithConversions
            ];
        } else {
            return [
                'status' => 'pending',
                'label' => 'Pending',
                'color' => 'red',
                'count' => $mediaCount,
                'converted' => $mediaWithConversions
            ];
        }
    }

    public function toggleFeatured($propertyId)
    {
        $property = Property::findOrFail($propertyId);
        $property->is_featured = !$property->is_featured;
        $property->save();
    }

    public function updateStatus($propertyId, $status)
    {
        $property = Property::findOrFail($propertyId);
        $property->status = $status;
        $property->save();
    }

    public function deleteProperty($propertyId)
    {
        Property::destroy($propertyId);
    }
}
