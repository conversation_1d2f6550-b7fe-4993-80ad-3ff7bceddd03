<?php

namespace App\Listeners;

use App\Events\PropertyIsLive;
use App\Models\User;
use App\Notifications\NewPropertyMatched;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class NotifyMatchingSeekers implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\PropertyIsLive  $event
     * @return void
     */
    public function handle(PropertyIsLive $event)
    {
        $property = $event->property;

        // Find seekers whose preferences match the new property
        $seekers = User::role('seeker')
            ->where(function ($query) use ($property) {
                $query->whereNull('budget_min')
                      ->orWhere('budget_min', '<=', $property->price);
            })
            ->where(function ($query) use ($property) {
                $query->whereNull('budget_max')
                      ->orWhere('budget_max', '>=', $property->price);
            })
            ->get();

        // Notify each matching seeker
        foreach ($seekers as $seeker) {
            $seeker->notify(new NewPropertyMatched($property));
        }
    }
}
