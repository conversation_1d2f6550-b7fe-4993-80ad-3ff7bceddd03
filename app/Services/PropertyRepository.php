<?php

namespace App\Services;

use App\Models\Property;
use App\Models\PropertyType;
use App\Models\PropertySubType;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class PropertyRepository
{
    /**
     * Get base query with standard relationships
     */
    public function baseQuery(): Builder
    {
        return Property::with(['user', 'propertySubType.propertyType', 'media', 'amenities']);
    }

    /**
     * Apply keyword search to query
     */
    public function applyKeywordSearch(Builder $query, string $keywords): Builder
    {
        return $query->where(function ($q) use ($keywords) {
            $q->where('title', 'like', '%' . $keywords . '%')
                ->orWhere('description', 'like', '%' . $keywords . '%')
                ->orWhere('city', 'like', '%' . $keywords . '%')
                ->orWhere('state_region', 'like', '%' . $keywords . '%');
        });
    }

    /**
     * Apply property type filtering
     */
    public function applyPropertyTypeFilter(Builder $query, ?string $propertyTypeId, ?string $propertySubTypeId): Builder
    {
        if ($propertySubTypeId) {
            return $query->where('property_sub_type_id', $propertySubTypeId);
        } elseif ($propertyTypeId) {
            return $query->whereHas('propertySubType', function ($q) use ($propertyTypeId) {
                $q->where('property_type_id', $propertyTypeId);
            });
        }

        return $query;
    }

    /**
     * Apply status filtering
     */
    public function applyStatusFilter(Builder $query, ?string $status): Builder
    {
        if ($status !== null && $status !== '') {
            return $query->where('status', $status);
        }

        return $query;
    }

    /**
     * Apply listing type filtering
     */
    public function applyListingTypeFilter(Builder $query, ?string $listingType): Builder
    {
        if ($listingType) {
            return $query->where('listing_type', $listingType);
        }

        return $query;
    }

    /**
     * Apply price range filtering
     */
    public function applyPriceRangeFilter(Builder $query, ?string $minPrice, ?string $maxPrice): Builder
    {
        if ($minPrice) {
            $query->where('price', '>=', $minPrice);
        }

        if ($maxPrice) {
            $query->where('price', '<=', $maxPrice);
        }

        return $query;
    }

    /**
     * Apply user's personal budget filters (for seekers)
     */
    public function applyUserBudgetFilters(Builder $query, ?User $user = null): Builder
    {
        $user = $user ?? Auth::user();

        if ($user && $user->hasRole('seeker')) {
            if ($user->budget_min) {
                $query->where('price', '>=', $user->budget_min);
            }

            if ($user->budget_max) {
                $query->where('price', '<=', $user->budget_max);
            }
        }

        return $query;
    }

    /**
     * Apply sorting to query
     */
    public function applySorting(Builder $query, string $sortField = 'created_at', string $sortDirection = 'desc'): Builder
    {
        return $query->orderBy($sortField, $sortDirection);
    }

    /**
     * Get published properties only
     */
    public function publishedOnly(Builder $query): Builder
    {
        return $query->where('status', 'published');
    }

    /**
     * Get properties for a specific user
     */
    public function forUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get all property types
     */
    public function getPropertyTypes(): Collection
    {
        return PropertyType::all();
    }

    /**
     * Get property sub-types for a specific property type
     */
    public function getPropertySubTypes(?string $propertyTypeId): Collection
    {
        if ($propertyTypeId) {
            return PropertySubType::where('property_type_id', $propertyTypeId)->get();
        }

        return collect();
    }

    /**
     * Build search query with all filters
     */
    public function buildSearchQuery(array $filters = []): Builder
    {
        $query = $this->baseQuery();

        // Apply keyword search
        if (!empty($filters['keywords'])) {
            $query = $this->applyKeywordSearch($query, $filters['keywords']);
        }

        // Apply property type filtering
        $query = $this->applyPropertyTypeFilter(
            $query,
            $filters['property_type_id'] ?? null,
            $filters['property_sub_type_id'] ?? null
        );

        // Apply listing type filtering
        if (!empty($filters['listing_type'])) {
            $query = $this->applyListingTypeFilter($query, $filters['listing_type']);
        }

        // Apply price range filtering
        $query = $this->applyPriceRangeFilter(
            $query,
            $filters['min_price'] ?? null,
            $filters['max_price'] ?? null
        );

        // Apply status filtering
        if (isset($filters['status'])) {
            $query = $this->applyStatusFilter($query, $filters['status']);
        }

        // Apply user budget filters if enabled
        if ($filters['apply_user_budget'] ?? false) {
            $query = $this->applyUserBudgetFilters($query);
        }

        // Apply sorting
        $query = $this->applySorting(
            $query,
            $filters['sort_by'] ?? 'created_at',
            $filters['sort_direction'] ?? 'desc'
        );

        return $query;
    }

    /**
     * Build admin management query
     */
    public function buildAdminQuery(array $filters = []): Builder
    {
        $query = $this->baseQuery();

        // Apply search
        if (!empty($filters['search'])) {
            $query = $this->applyKeywordSearch($query, $filters['search']);
        }

        // Apply property type filtering
        $query = $this->applyPropertyTypeFilter(
            $query,
            $filters['propertyTypeFilter'] ?? null,
            $filters['propertySubTypeFilter'] ?? null
        );

        // Apply status filtering
        if (isset($filters['statusFilter'])) {
            $query = $this->applyStatusFilter($query, $filters['statusFilter']);
        }

        // Apply sorting
        $query = $this->applySorting(
            $query,
            $filters['sortField'] ?? 'created_at',
            $filters['sortDirection'] ?? 'desc'
        );

        return $query;
    }

    /**
     * Get user's properties with relationships
     */
    public function getUserProperties(int $userId)
    {
        return Property::where('user_id', $userId)
            ->with(['propertySubType.propertyType', 'media'])
            ->paginate(10);
    }

    /**
     * Get user's favorite properties
     */
    public function getUserFavorites(User $user): Collection
    {
        return $user->favoritedProperties()
            ->with(['propertySubType.propertyType', 'media', 'user', 'amenities'])
            ->get();
    }

    /**
     * Convert properties to JavaScript-friendly format
     */
    public function toJavaScriptFormat(Collection $properties): array
    {
        return $properties->map(function ($property) {
            return $property->only([
                'id',
                'title',
                'latitude',
                'longitude'
            ]);
        })->all();
    }

    /**
     * Get all published properties with caching.
     */
    public function getAllPublishedPropertiesCached(): Collection
    {
        return Cache::remember('all_published_properties', 60, function () {
            return $this->publishedOnly(
                $this->baseQuery()
            )->get();
        });
    }
}
